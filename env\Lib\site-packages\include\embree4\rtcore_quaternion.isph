// Copyright 2009-2021 Intel Corporation
// SPDX-License-Identifier: Apache-2.0

#ifndef __EMBREE_QUATERNION_ISPH__
#define __EMBREE_QUATERNION_ISPH__

/*
 * Structure for transformation representation as a matrix decomposition using
 * a quaternion
 */
struct RTC_ALIGN(16) RTCQuaternionDecomposition
{
  float scale_x;
  float scale_y;
  float scale_z;
  float skew_xy;
  float skew_xz;
  float skew_yz;
  float shift_x;
  float shift_y;
  float shift_z;
  float quaternion_r;
  float quaternion_i;
  float quaternion_j;
  float quaternion_k;
  float translation_x;
  float translation_y;
  float translation_z;
};

RTC_FORCEINLINE void rtcInitQuaternionDecomposition(uniform RTCQuaternionDecomposition* uniform qdecomp)
{
  qdecomp->scale_x = 1.f;
  qdecomp->scale_y = 1.f;
  qdecomp->scale_z = 1.f;
  qdecomp->skew_xy = 0.f;
  qdecomp->skew_xz = 0.f;
  qdecomp->skew_yz = 0.f;
  qdecomp->shift_x = 0.f;
  qdecomp->shift_y = 0.f;
  qdecomp->shift_z = 0.f;
  qdecomp->quaternion_r = 1.f;
  qdecomp->quaternion_i = 0.f;
  qdecomp->quaternion_j = 0.f;
  qdecomp->quaternion_k = 0.f;
  qdecomp->translation_x = 0.f;
  qdecomp->translation_y = 0.f;
  qdecomp->translation_z = 0.f;
}

RTC_FORCEINLINE void rtcInitQuaternionDecomposition(uniform RTCQuaternionDecomposition* varying qdecomp)
{
  qdecomp->scale_x = 1.f;
  qdecomp->scale_y = 1.f;
  qdecomp->scale_z = 1.f;
  qdecomp->skew_xy = 0.f;
  qdecomp->skew_xz = 0.f;
  qdecomp->skew_yz = 0.f;
  qdecomp->shift_x = 0.f;
  qdecomp->shift_y = 0.f;
  qdecomp->shift_z = 0.f;
  qdecomp->quaternion_r = 1.f;
  qdecomp->quaternion_i = 0.f;
  qdecomp->quaternion_j = 0.f;
  qdecomp->quaternion_k = 0.f;
  qdecomp->translation_x = 0.f;
  qdecomp->translation_y = 0.f;
  qdecomp->translation_z = 0.f;
}

RTC_FORCEINLINE void rtcQuaternionDecompositionSetQuaternion(
  uniform RTCQuaternionDecomposition* uniform qdecomp,
  uniform float r, uniform float i, uniform float j, uniform float k)
{
  qdecomp->quaternion_r = r;
  qdecomp->quaternion_i = i;
  qdecomp->quaternion_j = j;
  qdecomp->quaternion_k = k;
}

RTC_FORCEINLINE void rtcQuaternionDecompositionSetQuaternion(
  uniform RTCQuaternionDecomposition* varying qdecomp,
  varying float r, varying float i, varying float j, varying float k)
{
  qdecomp->quaternion_r = r;
  qdecomp->quaternion_i = i;
  qdecomp->quaternion_j = j;
  qdecomp->quaternion_k = k;
}

RTC_FORCEINLINE void rtcQuaternionDecompositionSetScale(
  uniform RTCQuaternionDecomposition* uniform qdecomp,
  uniform float scale_x, uniform float scale_y, uniform float scale_z)
{
  qdecomp->scale_x = scale_x;
  qdecomp->scale_y = scale_y;
  qdecomp->scale_z = scale_z;
}

RTC_FORCEINLINE void rtcQuaternionDecompositionSetScale(
  uniform RTCQuaternionDecomposition* varying qdecomp,
  varying float scale_x, varying float scale_y, varying float scale_z)
{
  qdecomp->scale_x = scale_x;
  qdecomp->scale_y = scale_y;
  qdecomp->scale_z = scale_z;
}

RTC_FORCEINLINE void rtcQuaternionDecompositionSetSkew(
  uniform RTCQuaternionDecomposition* uniform qdecomp,
  uniform float skew_xy, uniform float skew_xz, uniform float skew_yz)
{
  qdecomp->skew_xy = skew_xy;
  qdecomp->skew_xz = skew_xz;
  qdecomp->skew_yz = skew_yz;
}

RTC_FORCEINLINE void rtcQuaternionDecompositionSetSkew(
  uniform RTCQuaternionDecomposition* varying qdecomp,
  varying float skew_xy, varying float skew_xz, varying float skew_yz)
{
  qdecomp->skew_xy = skew_xy;
  qdecomp->skew_xz = skew_xz;
  qdecomp->skew_yz = skew_yz;
}

RTC_FORCEINLINE void rtcQuaternionDecompositionSetShift(
  uniform RTCQuaternionDecomposition* uniform qdecomp,
  uniform float shift_x, uniform float shift_y, uniform float shift_z)
{
  qdecomp->shift_x = shift_x;
  qdecomp->shift_y = shift_y;
  qdecomp->shift_z = shift_z;
}

RTC_FORCEINLINE void rtcQuaternionDecompositionSetShift(
  uniform RTCQuaternionDecomposition* varying qdecomp,
  varying float shift_x, varying float shift_y, varying float shift_z)
{
  qdecomp->shift_x = shift_x;
  qdecomp->shift_y = shift_y;
  qdecomp->shift_z = shift_z;
}

RTC_FORCEINLINE void rtcQuaternionDecompositionSetTranslation(
  uniform RTCQuaternionDecomposition* uniform qdecomp,
  uniform float translation_x, uniform float translation_y, uniform float translation_z)
{
  qdecomp->translation_x = translation_x;
  qdecomp->translation_y = translation_y;
  qdecomp->translation_z = translation_z;
}

RTC_FORCEINLINE void rtcQuaternionDecompositionSetTranslation(
  uniform RTCQuaternionDecomposition* varying qdecomp,
  varying float translation_x, varying float translation_y, varying float translation_z)
{
  qdecomp->translation_x = translation_x;
  qdecomp->translation_y = translation_y;
  qdecomp->translation_z = translation_z;
}

#endif
