<mujoco model="franka_panda v200">
<!-- =================================================
    Copyright 2018 V<PERSON><PERSON>
    Model   :: <PERSON><PERSON> (MuJoCoV2.0)
    Author  :: <PERSON><PERSON><PERSON> (<EMAIL>)
    source  :: https://github.com/vikashplus/franka_sim
    License :: Under Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
====================================================== -->

    <include file="assets/basic_scene.xml"/>
    <include file="assets/assets_0.xml"/>
    <compiler meshdir=""/>

    <worldbody>
        <!-- <include file="assets/chain0.xml"/> -->
        
    <body name="panda0_link0" childclass="panda" pos="0 0 0" >
        <geom class="panda_viz" mesh="link0_viz"/>
        <geom class="panda_col" mesh="link0_col" mass="2.91242"/>
        <body name="panda0_link1" pos="0 0 0.333">
            <joint name="panda0_joint1" range="-2.8973 2.8973" class="panda_arm" damping="1.0"/>
            <geom class="panda_viz" mesh="link1_viz"/>
            <geom class="panda_col" mesh="link1_col" mass="2.7063"/>
            <body name="panda0_link2" pos="0 0 0" quat="0.707107 -0.707107 0 0">
                <joint name="panda0_joint2" range="-1.7628 1.7628" class="panda_arm" damping="1.0"/>
                <geom class="panda_viz" mesh="link2_viz"/>
                <geom class="panda_col" mesh="link2_col" mass="2.73046"/>
                <body name="panda0_link3" pos="0 -0.316 0" quat="0.707107 0.707107 0 0">
                    <joint name="panda0_joint3" range="-2.8973 2.8973" class="panda_arm" damping="1.0"/>
                    <geom class="panda_viz" mesh="link3_viz"/>
                    <geom class="panda_grey_viz" mesh="link3_dark_viz" pos="0 .001 0"/>
                    <geom class="panda_col" mesh="link3_col" mass="2.04104"/>
                    <body name="panda0_link4" pos="0.0825 0 0" quat="0.707107 0.707107 0 0">
                        <joint name="panda0_joint4" range="-3.0718 -0.4" class="panda_arm" damping="1.0"/>
                        <geom class="panda_viz" mesh="link4_viz"/>
                        <geom class="panda_grey_viz" mesh="link4_dark_viz"/>
                        <geom class="panda_col" mesh="link4_col" mass="2.08129"/>
                    </body>
                </body>
            </body>
        </body>
    </body>
    </worldbody>

    <include file='assets/actuator0_0.xml'/>

</mujoco>
