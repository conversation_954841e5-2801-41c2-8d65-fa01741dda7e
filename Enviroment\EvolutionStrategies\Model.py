import torch.nn as nn
import torch

class Linear(nn.Module):
    def __init__(self, input_dim: int, output_dim: int, num_agents: int):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.num_agents = num_agents
        self.weights = torch.randn(size=(num_agents, input_dim, output_dim)) #3D Tensor of weight matricies
        self.bias = torch.randn(size=(num_agents, output_dim))
        
        self.weights = nn.Parameter(self.weights, requires_grad=False)
        self.bias = nn.Parameter(self.bias, requires_grad=False)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = x.unsqueeze(1)
        return torch.bmm(x, self.weights).squeeze(1) + self.bias
        
class Model(nn.Module):
    def __init__(self, input_dim: int, output_dim: int, hidden_dim: int, num_hidden: int, num_agents: int, activ_func: nn.Module = nn.Tanh()):
        super().__init__() #inherits from torch.nn.Module, since its a nn
        layers = []
        in_dim = input_dim
        # Building the nn layers
        for _ in range(num_hidden):
            layers.append(Linear(in_dim, hidden_dim, num_agents))
            layers.append(activ_func)
            in_dim = hidden_dim
        layers.append(Linear(hidden_dim, output_dim, num_agents))
        layers.append(activ_func)

        self.net = nn.Sequential(*layers)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.net(x)
    

def get_models(input_dim: int, output_dim: int, hidden_dim: int, num_hidden: int, num_agents: int, device: str = "cuda", activ_func: nn.Module = nn.Tanh()) -> tuple[torch.nn.Module, torch.nn.Module]:
    policy = Model(input_dim, output_dim, hidden_dim, num_hidden, 1, activ_func).to(device)
    agents = Model(input_dim, output_dim, hidden_dim, num_hidden, num_agents, activ_func).to(device)
    return policy, agents