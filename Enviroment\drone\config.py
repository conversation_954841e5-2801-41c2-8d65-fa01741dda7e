def get_cfgs():
    env_cfg = {
        "num_actions": 4,
        # termination
        "termination_if_roll_greater_than": 180,  # degree
        "termination_if_pitch_greater_than": 180,
        "termination_if_close_to_ground": 0.1,
        "termination_if_x_greater_than": 3.0,
        "termination_if_y_greater_than": 3.0,
        "termination_if_z_greater_than": 2.0,
        # base pose
        "base_init_pos": [0.0, 0.0, 1.0],
        "base_init_quat": [1.0, 0.0, 0.0, 0.0],
        "episode_length_s": 15.0,
        "at_target_threshold": 0.1,
        "resampling_time_s": 3.0,
        "simulate_action_latency": True,
        "clip_actions": 1.0,
        # visualization
        "visualize_target": False,
        "visualize_camera": False,
        "max_visualize_FPS": 60,
    }
    obs_cfg = {
        "num_obs": 17,
        "obs_scales": {
            "rel_pos": 1 / 3.0,
            "lin_vel": 1 / 3.0,
            "ang_vel": 1 / 3.14159,
        },
    }
    reward_cfg = {
        "yaw_lambda": -2.0,  # Much less harsh yaw penalty for initial learning
        "reward_scales": {
            "target": 10.0,
            "smooth": -1e-3,  # Increased penalty magnitude for smoother actions
            "yaw": 0.1,  # Increased yaw reward scale
            "angular": -1e-3,  # Increased angular penalty magnitude
            "crash": -5.0,  # Reduced crash penalty to allow more exploration
        },
    }
    command_cfg = {
        "num_commands": 3,
        "pos_x_range": [-0.5, 0.5],  # Smaller range for easier initial learning
        "pos_y_range": [-0.5, 0.5],  # Smaller range for easier initial learning
        "pos_z_range": [0.8, 1.2],   # Allow some height variation
    }

    return env_cfg, obs_cfg, reward_cfg, command_cfg