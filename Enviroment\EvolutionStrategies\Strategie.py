import torch
from pathlib import Path
from torch.utils.tensorboard import SummaryWriter

class EvolutionStrategies:
    def __init__(self, iterations: int, num_agents: int, env: any, policy: torch.nn.Module, agents: torch.nn.Module, optimizer: any, device: str, sigma: float, num_env_steps_per_update: int, policy_save_path: Path):
        self.iterations = iterations
        self.num_agents = num_agents
        self.env = env
        self.policy = policy
        self.agents = agents
        self.optimizer = optimizer
        self.device = device
        self.sigma = sigma
        self.num_env_steps = num_env_steps_per_update
        self.policy_save_path = Path(policy_save_path)
        self.writer = SummaryWriter()

    def copy_policy_to_agents(self) -> None:
        """Copy policy parameters to agents"""
        for policy_param, agent_param in zip(self.policy.parameters(), self.agents.parameters()):
            # policy_param: [1, ...], agent_param: [num_agents, ...]
            # Properly broadcast policy parameters to all agents
            expanded = policy_param.data.expand_as(agent_param)
            agent_param.data.copy_(expanded)

    def perturbate(self) -> None:
        """Perturbate agent parameters and store noise for gradient calculation"""
        noise_list = []
        for agent_param in self.agents.parameters():
            # Generate noise with same shape as agent parameters
            noise = torch.randn_like(agent_param) * self.sigma
            noise_list.append(noise)
            # Add noise to agent parameters
            agent_param.data += noise

        # Store noise for gradient calculation in optimizer
        self.optimizer.store_noise(noise_list)

    def run_rollout(self) -> torch.Tensor:
        """Run rollout for all agents"""
        #---reset envs ---
        obs, _ = self.env.reset()
        obs = obs.to(self.device) #initial observation

        total_rewards = torch.zeros(self.num_agents, device=self.device) #1D tensor with reward per agent

        #---- rollout ----
        for _ in range(self.num_env_steps):
            # get actions from policy, reacting to observation
            actions = self.agents(obs)

            # step the environment, to let the agents execute the actions
            obs, rewards, dones, extras = self.env.step(actions)

            # (ensure tensors are on the correct device)
            obs = obs.to(self.device)
            rewards = rewards.to(self.device)

            # calculate rewards for agents that are still alive
            total_rewards += rewards

        return total_rewards

    def run(self) -> None:
        """Run the evolution strategy"""
        for iteration in range(self.iterations):
            #--- copy policy to agents ---
            self.copy_policy_to_agents()

            #--- perturbate params ---
            self.perturbate()

            # --- rollout ---
            total_rewards = self.run_rollout()

            # normalize rewards
            reward_std = total_rewards.std()
            reward_mean = total_rewards.mean()

            if reward_std < 1e-8 or self.num_agents == 1:
                rewards = total_rewards - reward_mean
            else:
                rewards = (total_rewards - reward_mean) / reward_std

            # --- update policy ---
            self.optimizer.step(rewards)

            if iteration % 100 == 0:
                torch.save(self.policy.state_dict(), self.policy_save_path / "policy.pt")

            self.writer.add_scalar("reward/mean", reward_mean, iteration)
            self.writer.add_scalar("reward/std", reward_std, iteration)
            self.writer.add_scalar("reward/max", total_rewards.max(), iteration)
            self.writer.add_scalar("reward/min", total_rewards.min(), iteration)

            print(f"Iteration {iteration+1}/{self.iterations}: "
                f"mean_reward={reward_mean.item():.4f}, "
                f"std_reward={reward_std.item():.4f}, "
                f"max_reward={total_rewards.max().item():.4f}, "
                f"min_reward={total_rewards.min().item():.4f}")

            # Curriculum learning: gradually make yaw penalty harsher as learning progresses
            if iteration > 0 and iteration % 500 == 0 and reward_mean.item() > -0.1:
                current_yaw_lambda = self.env.reward_cfg["yaw_lambda"]
                if current_yaw_lambda > -8.0:  # Don't go beyond -8.0
                    self.env.reward_cfg["yaw_lambda"] = current_yaw_lambda - 1.0
                    print(f"Curriculum: Increased yaw penalty to {self.env.reward_cfg['yaw_lambda']}")