import torch

class SGD:
    def __init__(self, policy: torch.nn.Module, agents: torch.nn.Module, learning_rate: float, sigma: float, num_agents: int, device: str):
        self.policy = policy
        self.agents = agents
        self.learning_rate = learning_rate
        self.sigma = sigma
        self.num_agents = num_agents
        self.device = device

    def step(self, rewards: torch.Tensor)-> None:

        for params, noise in zip(self.policy.parameters(), self.agents.parameters()):

            # es gradient estimate: ∇_θ J(θ) ≈ (1 / (N σ)) ∑ᵢ Rᵢ εᵢ
            # rewards: [num_agents], noise: [num_agents, ...param_dims]
            rewards_shape = [self.num_agents] + [1] * (noise.dim() - 1)
            rewards_reshaped = rewards.view(rewards_shape)

            gradient = torch.sum(rewards_reshaped * noise, dim=0) / (self.num_agents * self.sigma)

            # update flat_params (gradient ascend)
            max_update = 0.1
            update = self.learning_rate * gradient
            update = torch.clamp(update, -max_update, max_update)
            params += update