#!/usr/bin/env python3
"""
Test script to verify model saving and loading works correctly
"""
import torch
import genesis as gs
from pathlib import Path
from drone import get_cfgs
from EvolutionStrategies import get_models

def test_model_save_load():
    """Test that model saving and loading preserves parameters"""
    
    # Initialize genesis
    gs.init(logging_level="warning")
    
    # Get configs
    env_cfg, obs_cfg, reward_cfg, command_cfg = get_cfgs()
    
    # Model parameters
    hidden_dim = 64
    num_hidden = 3
    policy_save_path = Path(r"C:\Praktikum\Enviroment\trained_policy")
    policy_save_path.mkdir(exist_ok=True)
    
    # Create original model
    print("Creating original model...")
    policy_orig, _ = get_models(
        num_agents=1,
        input_dim=obs_cfg["num_obs"],
        output_dim=env_cfg["num_actions"],
        hidden_dim=hidden_dim,
        num_hidden=num_hidden,
        device=gs.device
    )
    
    # Get original parameters
    orig_params = {}
    for name, param in policy_orig.named_parameters():
        orig_params[name] = param.clone()
    
    print(f"Original model has {sum(p.numel() for p in policy_orig.parameters())} parameters")
    
    # Save model
    print("Saving model...")
    torch.save(policy_orig.state_dict(), policy_save_path / "test_policy.pt")
    
    # Create new model
    print("Creating new model...")
    policy_new, _ = get_models(
        num_agents=1,
        input_dim=obs_cfg["num_obs"],
        output_dim=env_cfg["num_actions"],
        hidden_dim=hidden_dim,
        num_hidden=num_hidden,
        device=gs.device
    )
    
    # Load saved parameters
    print("Loading saved parameters...")
    state_dict = torch.load(policy_save_path / "test_policy.pt")
    policy_new.load_state_dict(state_dict)
    
    # Compare parameters
    print("Comparing parameters...")
    all_match = True
    for name, param in policy_new.named_parameters():
        if not torch.allclose(param, orig_params[name]):
            print(f"Parameter {name} does not match!")
            all_match = False
    
    if all_match:
        print("✅ SUCCESS: All parameters match after save/load!")
    else:
        print("❌ FAILURE: Some parameters don't match!")
    
    # Test with dummy input
    print("Testing forward pass...")
    dummy_input = torch.randn(1, obs_cfg["num_obs"], device=gs.device)
    
    with torch.no_grad():
        output_orig = policy_orig(dummy_input)
        output_new = policy_new(dummy_input)
    
    if torch.allclose(output_orig, output_new):
        print("✅ SUCCESS: Forward pass outputs match!")
    else:
        print("❌ FAILURE: Forward pass outputs don't match!")
        print(f"Original output: {output_orig}")
        print(f"New output: {output_new}")
    
    # Clean up
    (policy_save_path / "test_policy.pt").unlink(missing_ok=True)
    
    return all_match

if __name__ == "__main__":
    torch.manual_seed(42)  # For reproducible results
    success = test_model_save_load()
    if success:
        print("\n🎉 Model save/load test PASSED!")
    else:
        print("\n💥 Model save/load test FAILED!")
