import torch
import argparse
import os
import pickle
import shutil
from pathlib import Path

import genesis as gs

from drone import get_cfgs, HoverEnv
from EvolutionStrategies import get_models, SGD, EvolutionStrategies

def run_rollout(env: HoverEnv, policy: torch.nn.Module, num_env_steps: int) -> torch.Tensor:
        """Run rollout for policy"""
        #---reset envs ---
        obs, _ = env.reset()

        #---- rollout ----
        for _ in range(num_env_steps):
            # get actions from policy, reacting to observation
            actions = policy(obs)

            # step the environment, to let the agents execute the actions
            obs, rewards, dones, extras = env.step(actions)

def main() -> None:
    parser = argparse.ArgumentParser()
    parser.add_argument("-e", "--exp_name", type=str, default="target")
    parser.add_argument("-B", "--num_envs", type=int, default=1)
    args = parser.parse_args()

    gs.init(logging_level="warning")

    log_dir = f"logs/{args.exp_name}"
    env_cfg, obs_cfg, reward_cfg, command_cfg = get_cfgs()

    env_cfg["visualize_target"] = True

    if os.path.exists(log_dir):
        shutil.rmtree(log_dir)
    os.makedirs(log_dir, exist_ok=True)

    pickle.dump(
        [env_cfg, obs_cfg, reward_cfg, command_cfg],
        open(f"{log_dir}/cfgs.pkl", "wb"),
    )

    env = HoverEnv(
        num_envs=1,
        env_cfg=env_cfg,
        obs_cfg=obs_cfg,
        reward_cfg=reward_cfg,
        command_cfg=command_cfg,
        show_viewer=True,
    )

    num_env_steps_per_update = 100
    hidden_dim = 64                 
    num_hidden = 3
    policy_save_path = Path(r"C:\Praktikum\Enviroment\trained_policy")


    policy, _ = get_models(
        num_agents=args.num_envs,
        input_dim=obs_cfg["num_obs"],
        output_dim=env_cfg["num_actions"],
        hidden_dim=hidden_dim,
        num_hidden=num_hidden,
        device=gs.device
    )

    print(f"Policy läuft auf dem Gerät: {next(policy.parameters()).device}")
    print(f"Policy Architektur: {policy.net}")


    state_dict = torch.load(policy_save_path / "policy.pt")
    policy.load_state_dict(state_dict)

    run_rollout(env, policy, num_env_steps_per_update)

if __name__ == "__main__":
    torch.manual_seed(seed=0)
    torch.set_float32_matmul_precision("high")
    main()
