// Copyright 2009-2021 Intel Corporation
// SPDX-License-Identifier: Apache-2.0

#ifndef __RTC_DEVICE_ISPH__
#define __RTC_DEVICE_ISPH__

#include "rtcore_common.isph"

/* Opaque device type */
typedef uniform struct RTCDeviceTy* uniform RTCDevice;

/* Creates a new Embree device. */
RTC_API RTCDevice rtcNewDevice(const uniform int8* uniform config);

/* Retains the Embree device (increments the reference count). */
RTC_API void rtcRetainDevice(RTCDevice device);
  
/* Releases an Embree device (decrements the reference count). */
RTC_API void rtcReleaseDevice(RTCDevice device);

/* Device properties */
enum RTCDeviceProperty
{
  RTC_DEVICE_PROPERTY_VERSION       = 0,
  RTC_DEVICE_PROPERTY_VERSION_MAJOR = 1,
  RTC_DEVICE_PROPERTY_VERSION_MINOR = 2,
  RTC_DEVICE_PROPERTY_VERSION_PATCH = 3,

  RTC_DEVICE_PROPERTY_NATIVE_RAY4_SUPPORTED  = 32,
  RTC_DEVICE_PROPERTY_NATIVE_RAY8_SUPPORTED  = 33,
  RTC_DEVICE_PROPERTY_NATIVE_RAY16_SUPPORTED = 34,

  RTC_DEVICE_PROPERTY_BACKFACE_CULLING_CURVES_ENABLED = 63,
  RTC_DEVICE_PROPERTY_RAY_MASK_SUPPORTED          = 64,
  RTC_DEVICE_PROPERTY_BACKFACE_CULLING_ENABLED    = 65,
  RTC_DEVICE_PROPERTY_FILTER_FUNCTION_SUPPORTED   = 66,
  RTC_DEVICE_PROPERTY_IGNORE_INVALID_RAYS_ENABLED = 67,
  RTC_DEVICE_PROPERTY_COMPACT_POLYS_ENABLED       = 68,

  RTC_DEVICE_PROPERTY_TRIANGLE_GEOMETRY_SUPPORTED    = 96,
  RTC_DEVICE_PROPERTY_QUAD_GEOMETRY_SUPPORTED        = 97,
  RTC_DEVICE_PROPERTY_SUBDIVISION_GEOMETRY_SUPPORTED = 98,
  RTC_DEVICE_PROPERTY_CURVE_GEOMETRY_SUPPORTED       = 99,
  RTC_DEVICE_PROPERTY_USER_GEOMETRY_SUPPORTED        = 100,

  RTC_DEVICE_PROPERTY_TASKING_SYSTEM        = 128,
  RTC_DEVICE_PROPERTY_JOIN_COMMIT_SUPPORTED = 129,
  RTC_DEVICE_PROPERTY_PARALLEL_COMMIT_SUPPORTED = 130
};

/* Gets a device property. */
RTC_API uniform intptr_t rtcGetDeviceProperty(RTCDevice device, uniform RTCDeviceProperty prop);

/* Sets a device property. */
RTC_API void rtcSetDeviceProperty(RTCDevice device, const uniform RTCDeviceProperty prop, uniform intptr_t value);

/* Error codes */
enum RTCError
{
  RTC_ERROR_NONE              = 0,
  RTC_ERROR_UNKNOWN           = 1,
  RTC_ERROR_INVALID_ARGUMENT  = 2,
  RTC_ERROR_INVALID_OPERATION = 3,
  RTC_ERROR_OUT_OF_MEMORY     = 4,
  RTC_ERROR_UNSUPPORTED_CPU   = 5,
  RTC_ERROR_CANCELLED         = 6
};

/* Returns the error code. */
RTC_API uniform RTCError rtcGetDeviceError(RTCDevice device);

/* Error callback function */
typedef unmasked void (*uniform RTCErrorFunction)(void* uniform userPtr, uniform RTCError code, const uniform int8* uniform str);

/* Sets the error callback function. */
RTC_API void rtcSetDeviceErrorFunction(RTCDevice device, uniform RTCErrorFunction error, void* uniform userPtr);

/* Memory monitor callback function */
typedef unmasked uniform bool (*uniform RTCMemoryMonitorFunction)(void* uniform ptr, uniform intptr_t bytes, uniform bool post);

/* Sets the memory monitor callback function. */
RTC_API void rtcSetDeviceMemoryMonitorFunction(RTCDevice device, RTCMemoryMonitorFunction memoryMonitor, void* uniform userPtr);

#endif
